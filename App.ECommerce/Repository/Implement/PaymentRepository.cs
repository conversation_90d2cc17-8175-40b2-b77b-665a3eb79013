﻿using App.Base.Repository;
using App.Base.Utilities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units;
using AutoMapper;
using MongoDB.Bson;
using MongoDB.Driver;

namespace App.ECommerce.Repository.Implement;

public class PaymentRepository : BaseRepository, IPaymentRepository
{
    private readonly IMongoCollection<Payment> _collectionPayment;
    private readonly IMapper _mapper;

    public PaymentRepository(IMapper mapper) : base()
    {
        _mapper = mapper;
        _collectionPayment = _database.GetCollection<Payment>($"Payment");

        var indexOptions = new CreateIndexOptions();
        var indexModelItems = new List<CreateIndexModel<Payment>>()
        {
            new CreateIndexModel<Payment>(Builders<Payment>.IndexKeys.Ascending(item => item.PaymentId), indexOptions),
            new CreateIndexModel<Payment>(Builders<Payment>.IndexKeys.Ascending(item => item.IsActive), indexOptions),
            new CreateIndexModel<Payment>(Builders<Payment>.IndexKeys.Ascending(item => item.Name), indexOptions),
            new CreateIndexModel<Payment>(Builders<Payment>.IndexKeys.Ascending(item => item.Detail), indexOptions),
        };
        _collectionPayment.Indexes.CreateMany(indexModelItems);
    }

    //=== Payment

    #region Payment

    public Payment CreateItems(Payment item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.PaymentId = Guid.NewGuid().ToString();
        item.Name = item.Name;

        if (item.Platform != null)
        {
            item.Platform = item.Platform.Distinct().ToList();
        }
        else
        {
            item.Platform = new List<string>();
        }

        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionPayment.InsertOne(item);
        return item;
    }

    public Payment DeleteItems(string itemsId)
    {
        return _collectionPayment.FindOneAndDelete(item => item.PaymentId == itemsId);
    }

    public Payment? FindByItemsId(string itemsId)
    {
        return _collectionPayment.Find(item => item.PaymentId == itemsId).FirstOrDefault();
    }

    public Payment? FindByPlatform(string shopId, string platform)
    {
        return _collectionPayment.Find(item => item.Platform.Contains(platform) && item.IsActive && item.ShopId == shopId)
            .Sort(Builders<Payment>.Sort.Ascending(p => p.Position))
            .FirstOrDefault();
    }

    public PagingResult<Payment> ListItems(Paging paging, string? shopId = null)
    {
        PagingResult<Payment> result = new PagingResult<Payment>();
        FilterDefinition<Payment> filterBuilders = Builders<Payment>.Filter.And(
            Builders<Payment>.Filter.Where(p => shopId == null || p.ShopId == shopId),
            //Builders<Payment>.Filter.Where(p => p.IsActive == true),
            Builders<Payment>.Filter.Or(
                Builders<Payment>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                Builders<Payment>.Filter.Regex(x => x.Name,
                    new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(),
                        "i"))
            )
        );
        var query = _collectionPayment.Find(filterBuilders);
        result.Total = query.ToList().Count;
        result.Result = query.Sort(Builders<Payment>.Sort.Ascending(p => p.Position))
            .Skip(paging.PageIndex * paging.PageSize)
            .Limit(paging.PageSize)
            .ToList();
        return result;
    }
    public PagingResult<Payment> ListItemsUser(Paging paging, string shopId, PaymentPlatformType paymentPlatformType)
    {
        PagingResult<Payment> result = new PagingResult<Payment>();
        FilterDefinition<Payment> filterBuilders = Builders<Payment>.Filter.And(
            Builders<Payment>.Filter.Where(p => p.ShopId == shopId),
            Builders<Payment>.Filter.Where(p => p.IsActive == true),
            Builders<Payment>.Filter.Where(p => p.Platform.Contains(paymentPlatformType.ToString())),
            Builders<Payment>.Filter.Or(
                Builders<Payment>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                Builders<Payment>.Filter.Regex(x => x.Name,
                    new BsonRegularExpression($@"{(paging.Search?.NonUnicode().ToLower() ?? "")}".EscapeSpecialChars(),
                        "i"))
            )
        );

        var query = _collectionPayment.Find(filterBuilders);
        result.Total = query.ToList().Count;
        result.Result = query.Sort(Builders<Payment>.Sort.Ascending(x => x.Position))
            .Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;
    }

    public Payment? UpdateItems(Payment item)
    {
        Payment _item = _collectionPayment.Find(x => x.PaymentId == item.PaymentId).FirstOrDefault();
        if (_item == null) return null;

        if (item.Platform != null)
        {
            item.Platform = item.Platform.Distinct().ToList();
        }
        else
        {
            item.Platform = new List<string>();
        }

        var update = Builders<Payment>.Update
            .Set("PaymentId", item.PaymentId)
            .Set("Name", item.Name)
            .Set("IsActive", item.IsActive)
            .Set("Position", item.Position)
            .Set("Detail", item.Detail)
            .Set("ShopId", item.ShopId)
            .Set("Created", _item.Created)
            .Set("PaymentPhoto", item.PaymentPhoto)
            .Set("Platform", item.Platform)
            .Set("TypePay", item.TypePay)
            .Set("BankAccountNumber", item.BankAccountNumber)
            .Set("BankShortCode", item.BankShortCode)
            .Set("CustomerBankName", item.CustomerBankName)
            .Set("IdentificationNumber", item.IdentificationNumber)
            .Set("PhoneNumber", item.PhoneNumber)
            .Set("Alias", item.Alias)
            .Set("BankAccountId", item.BankAccountId)
            .Set("BankName", item.BankName)
            .Set("BankNumber", item.BankNumber)
            .Set("BankUserName", item.BankUserName)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<Payment>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<Payment> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return _collectionPayment.FindOneAndUpdate(filter, update, options);
    }
    #endregion Payment ./



}